@echo off
echo Installing PatokRebinmasMobile on connected Android device...

REM Add Android SDK platform-tools to PATH
set PATH=%PATH%;%USERPROFILE%\AppData\Local\Android\Sdk\platform-tools

REM Check if device is connected
echo Checking connected devices...
adb devices

REM Install the APK with necessary flags
echo Installing APK...
adb install -r -t -g android\app\build\outputs\apk\debug\app-debug.apk

REM Start the app
echo Starting the app...
adb shell am start -n com.patokrebinmasmobile/.MainActivity

echo Done! The app should now be running on your device.
pause
