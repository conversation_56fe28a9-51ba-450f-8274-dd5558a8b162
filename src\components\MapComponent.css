.map-container {
  height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  font-size: 1.5rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.loading-overlay::after {
  content: "";
  width: 30px;
  height: 30px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  margin-left: 10px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 0, 0, 0.7);
  color: white;
  padding: 1rem;
  text-align: center;
  z-index: 1000;
  font-size: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.location-marker {
  display: flex;
  justify-content: center;
  align-items: center;
}

.location-marker-inner {
  width: 12px;
  height: 12px;
  background-color: #3388ff;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    box-shadow: 0 0 0 0 rgba(51, 136, 255, 0.7);
  }
  70% {
    transform: scale(1.2);
    box-shadow: 0 0 0 10px rgba(51, 136, 255, 0);
  }
  100% {
    transform: scale(0.8);
    box-shadow: 0 0 0 0 rgba(51, 136, 255, 0);
  }
}

.leaflet-container {
  height: 100%;
  width: 100%;
}

.leaflet-popup-content {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.leaflet-popup-content h3 {
  margin: 0 0 10px 0;
  color: #007bff;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.leaflet-popup-content h4 {
  margin: 10px 0 5px 0;
  color: #555;
}

.leaflet-popup-content p {
  margin: 5px 0;
}

.leaflet-popup-content ul {
  margin: 5px 0;
  padding-left: 20px;
}

.leaflet-popup-content li {
  margin-bottom: 3px;
}

.action-buttons button {
  transition: all 0.2s ease;
}

.action-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.4);
}

.action-buttons button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

/* Tombol layer switcher */
.layer-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
}

/* Tombol locate */
.locate-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  color: #007bff;
}

/* Responsive styling */
@media (max-width: 480px) {
  .action-buttons {
    width: calc(100% - 20px);
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: space-between;
  }
  
  .action-buttons button {
    font-size: 12px;
    padding: 8px;
  }
}
