import React, { useState, useEffect, useCallback } from 'react';
import { StyleSheet, View, Text, ActivityIndicator, Alert } from 'react-native';
import MapView, { Marker, Polygon } from 'react-native-maps';
import Geolocation from 'react-native-geolocation-service';
import RNFS from 'react-native-fs';
import * as shpjs from 'shpjs';

// For converting base64 to Buffer (needed for shpjs)
const Buffer = global.Buffer || require('buffer').Buffer;

const MapComponent = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [userLocation, setUserLocation] = useState(null);
  const [polygonData, setPolygonData] = useState([]);
  const [markerData, setMarkerData] = useState([]);
  const [errorMsg, setErrorMsg] = useState(null);

  // Initial map region (will be updated once we get user location)
  const [region, setRegion] = useState({
    latitude: -2.988, // Default to Indonesia
    longitude: 104.756,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });

  // Get user location
  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setUserLocation({ latitude, longitude });
        setRegion({
          latitude,
          longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      },
      (error) => {
        console.log(error);
        setErrorMsg('Error getting current location');
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  };

  // Copy shapefile if it doesn't exist in the document directory
  const copyShapefileIfNeeded = useCallback(async (basename) => {
    const destShp = RNFS.DocumentDirectoryPath + `/${basename}.shp`;
    const destDbf = RNFS.DocumentDirectoryPath + `/${basename}.dbf`;
    const destShx = RNFS.DocumentDirectoryPath + `/${basename}.shx`;
    
    // Check if files already exist
    const shpExists = await RNFS.exists(destShp);
    const dbfExists = await RNFS.exists(destDbf);
    const shxExists = await RNFS.exists(destShx);
    
    if (!shpExists || !dbfExists || !shxExists) {
      // Copy files from assets
      try {
        await RNFS.copyFileAssets(`shapefile/${basename}.shp`, destShp);
        await RNFS.copyFileAssets(`shapefile/${basename}.dbf`, destDbf);
        await RNFS.copyFileAssets(`shapefile/${basename}.shx`, destShx);
        console.log(`Copied ${basename} shapefile files to document directory`);
      } catch (error) {
        console.error(`Error copying ${basename} shapefile files:`, error);
        throw error;
      }
    }
  }, []);

  // Load shapefile data
  const loadShapefileData = useCallback(async () => {
    try {
      setIsLoading(true);

      // Path to polygon shapefile
      const polygonShpPath = RNFS.DocumentDirectoryPath + '/shp1999_clipped.shp';
      const polygonDbfPath = RNFS.DocumentDirectoryPath + '/shp1999_clipped.dbf';
      const polygonShxPath = RNFS.DocumentDirectoryPath + '/shp1999_clipped.shx';
      
      // Path to marker shapefile
      const markerShpPath = RNFS.DocumentDirectoryPath + '/combined_markers_20250517_075624.shp';
      const markerDbfPath = RNFS.DocumentDirectoryPath + '/combined_markers_20250517_075624.dbf';
      const markerShxPath = RNFS.DocumentDirectoryPath + '/combined_markers_20250517_075624.shx';

      // Check if files exist in Document directory, if not copy them from assets
      await copyShapefileIfNeeded('shp1999_clipped');
      await copyShapefileIfNeeded('combined_markers_20250517_075624');

      // Load polygon data
      const polygonShpData = await RNFS.readFile(polygonShpPath, 'base64');
      const polygonDbfData = await RNFS.readFile(polygonDbfPath, 'base64');
      const polygonShxData = await RNFS.readFile(polygonShxPath, 'base64');

      // Load marker data
      const markerShpData = await RNFS.readFile(markerShpPath, 'base64');
      const markerDbfData = await RNFS.readFile(markerDbfPath, 'base64');
      const markerShxData = await RNFS.readFile(markerShxPath, 'base64');

      // Parse polygon shapefile
      const polygonGeojson = await shpjs.combine([
        await shpjs.parseShp(Buffer.from(polygonShpData, 'base64'), Buffer.from(polygonShxData, 'base64')),
        await shpjs.parseDbf(Buffer.from(polygonDbfData, 'base64'))
      ]);

      // Parse marker shapefile
      const markerGeojson = await shpjs.combine([
        await shpjs.parseShp(Buffer.from(markerShpData, 'base64'), Buffer.from(markerShxData, 'base64')),
        await shpjs.parseDbf(Buffer.from(markerDbfData, 'base64'))
      ]);

      // Process polygon data
      if (polygonGeojson && polygonGeojson.features) {
        const processedPolygons = polygonGeojson.features.map(feature => {
          if (feature.geometry && feature.geometry.type === 'Polygon') {
            return feature.geometry.coordinates[0].map(coord => ({
              latitude: coord[1],
              longitude: coord[0]
            }));
          }
          return [];
        }).filter(polygon => polygon.length > 0);
        
        setPolygonData(processedPolygons);
      }

      // Process marker data
      if (markerGeojson && markerGeojson.features) {
        const processedMarkers = markerGeojson.features.map(feature => {
          if (feature.geometry && feature.geometry.type === 'Point') {
            return {
              coordinate: {
                latitude: feature.geometry.coordinates[1],
                longitude: feature.geometry.coordinates[0]
              },
              title: feature.properties.name || 'Marker',
              description: feature.properties.description || 'Boundary Marker'
            };
          }
          return null;
        }).filter(marker => marker !== null);
        
        setMarkerData(processedMarkers);
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error loading shapefile data:', error);
      setErrorMsg('Error loading map data: ' + error.message);
      setIsLoading(false);
    }
  }, [copyShapefileIfNeeded, setIsLoading, setPolygonData, setMarkerData, setErrorMsg]);

  useEffect(() => {
    // Load shapefile data
    loadShapefileData();

    // Get user location
    getCurrentLocation();

    // Set up location updates
    const watchId = Geolocation.watchPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setUserLocation({ latitude, longitude });
        setRegion({
          latitude,
          longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      },
      (error) => {
        console.log(error);
        setErrorMsg('Error getting location updates');
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 10, // Update if user moves by 10 meters
        interval: 5000, // Update every 5 seconds
        fastestInterval: 2000,
      }
    );

    // Cleanup
    return () => {
      Geolocation.clearWatch(watchId);
    };
  }, [loadShapefileData]); // Add loadShapefileData as a dependency

  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loadingText}>Loading map data...</Text>
        </View>
      ) : errorMsg ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{errorMsg}</Text>
        </View>
      ) : (
        <MapView
          style={styles.map}
          region={region}
          showsUserLocation={true}
          followsUserLocation={true}
        >
          {/* Render polygon area */}
          {polygonData.map((polygon, index) => (
            <Polygon
              key={`polygon-${index}`}
              coordinates={polygon}
              strokeColor="rgba(0, 0, 255, 0.8)"
              fillColor="rgba(0, 0, 255, 0.2)"
              strokeWidth={2}
            />
          ))}

          {/* Render boundary markers */}
          {markerData.map((marker, index) => (
            <Marker
              key={`marker-${index}`}
              coordinate={marker.coordinate}
              title={marker.title}
              description={marker.description}
              pinColor="red"
            />
          ))}

          {/* Render user location if available */}
          {userLocation && (
            <Marker
              coordinate={userLocation}
              title="You are here"
              description="Your current location"
              pinColor="blue"
            />
          )}
        </MapView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
  },
});

export default MapComponent;
