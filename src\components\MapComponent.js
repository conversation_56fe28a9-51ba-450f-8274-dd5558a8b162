import React, { useState, useEffect, useCallback } from 'react';
import { StyleSheet, View, Text, ActivityIndicator, Alert } from 'react-native';
import MapView, { Marker, Polygon, UrlTile } from 'react-native-maps';
import Geolocation from 'react-native-geolocation-service';

// Sample GeoJSON data for testing (replace with actual converted data)
const samplePolygonData = {
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": {
        "type": "Polygon",
        "coordinates": [[
          [104.756, -2.988],
          [104.766, -2.988],
          [104.766, -2.978],
          [104.756, -2.978],
          [104.756, -2.988]
        ]]
      },
      "properties": {
        "name": "Sample Area"
      }
    }
  ]
};

const sampleMarkerData = {
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": {
        "type": "Point",
        "coordinates": [104.761, -2.983]
      },
      "properties": {
        "name": "Boundary Marker 1",
        "description": "Sample boundary marker"
      }
    },
    {
      "type": "Feature",
      "geometry": {
        "type": "Point",
        "coordinates": [104.763, -2.985]
      },
      "properties": {
        "name": "Boundary Marker 2",
        "description": "Sample boundary marker"
      }
    }
  ]
};

const MapComponent = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [userLocation, setUserLocation] = useState(null);
  const [polygonData, setPolygonData] = useState([]);
  const [markerData, setMarkerData] = useState([]);
  const [errorMsg, setErrorMsg] = useState(null);

  // Initial map region (will be updated once we get user location)
  const [region, setRegion] = useState({
    latitude: -2.988, // Default to Indonesia
    longitude: 104.756,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });

  // Get user location
  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setUserLocation({ latitude, longitude });
        setRegion({
          latitude,
          longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      },
      (error) => {
        console.log(error);
        setErrorMsg('Error getting current location');
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  };

  // Load GeoJSON data (simplified approach)
  const loadGeoJSONData = useCallback(async () => {
    try {
      setIsLoading(true);

      // Process polygon data from sample data
      if (samplePolygonData && samplePolygonData.features) {
        const processedPolygons = samplePolygonData.features.map(feature => {
          if (feature.geometry && feature.geometry.type === 'Polygon') {
            return feature.geometry.coordinates[0].map(coord => ({
              latitude: coord[1],
              longitude: coord[0]
            }));
          }
          return [];
        }).filter(polygon => polygon.length > 0);

        setPolygonData(processedPolygons);
      }

      // Process marker data from sample data
      if (sampleMarkerData && sampleMarkerData.features) {
        const processedMarkers = sampleMarkerData.features.map(feature => {
          if (feature.geometry && feature.geometry.type === 'Point') {
            return {
              coordinate: {
                latitude: feature.geometry.coordinates[1],
                longitude: feature.geometry.coordinates[0]
              },
              title: feature.properties.name || 'Marker',
              description: feature.properties.description || 'Boundary Marker'
            };
          }
          return null;
        }).filter(marker => marker !== null);

        setMarkerData(processedMarkers);
      }

      setIsLoading(false);
      console.log('GeoJSON data loaded successfully');
    } catch (error) {
      console.error('Error loading GeoJSON data:', error);
      setErrorMsg('Error loading map data: ' + error.message);
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Load GeoJSON data
    loadGeoJSONData();

    // Get user location
    getCurrentLocation();

    // Set up location updates
    const watchId = Geolocation.watchPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setUserLocation({ latitude, longitude });
        setRegion({
          latitude,
          longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      },
      (error) => {
        console.log(error);
        setErrorMsg('Error getting location updates');
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 10, // Update if user moves by 10 meters
        interval: 5000, // Update every 5 seconds
        fastestInterval: 2000,
      }
    );

    // Cleanup
    return () => {
      Geolocation.clearWatch(watchId);
    };
  }, [loadGeoJSONData]); // Add loadGeoJSONData as a dependency

  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loadingText}>Loading map data...</Text>
        </View>
      ) : errorMsg ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{errorMsg}</Text>
        </View>
      ) : (
        <MapView
          style={styles.map}
          region={region}
          showsUserLocation={true}
          followsUserLocation={true}
          mapType="none"
        >
          {/* OpenStreetMap tiles */}
          <UrlTile
            urlTemplate="https://tile.openstreetmap.org/{z}/{x}/{y}.png"
            maximumZ={19}
            flipY={false}
          />
          {/* Render polygon area */}
          {polygonData.map((polygon, index) => (
            <Polygon
              key={`polygon-${index}`}
              coordinates={polygon}
              strokeColor="rgba(0, 0, 255, 0.8)"
              fillColor="rgba(0, 0, 255, 0.2)"
              strokeWidth={2}
            />
          ))}

          {/* Render boundary markers */}
          {markerData.map((marker, index) => (
            <Marker
              key={`marker-${index}`}
              coordinate={marker.coordinate}
              title={marker.title}
              description={marker.description}
              pinColor="red"
            />
          ))}

          {/* Render user location if available */}
          {userLocation && (
            <Marker
              coordinate={userLocation}
              title="You are here"
              description="Your current location"
              pinColor="blue"
            />
          )}
        </MapView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
  },
});

export default MapComponent;
