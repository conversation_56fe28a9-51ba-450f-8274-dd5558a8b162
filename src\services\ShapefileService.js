import RNFS from 'react-native-fs';
import * as shpjs from 'shpjs';

// For converting base64 to Buffer (needed for shpjs)
const Buffer = global.Buffer || require('buffer').Buffer;

class ShapefileService {
  constructor() {
    this.polygonData = null;
    this.markerData = null;
    this.isLoading = false;
    this.error = null;
  }

  /**
   * Get shapefile data from GeoJSON or convert from shapefile if needed
   * @returns {Promise<Object>} - The polygon data in GeoJSON format
   */
  async getShapefileData() {
    try {
      // Coba ambil dari cache atau GeoJSON file langsung
      let response = await fetch(`${process.env.PUBLIC_URL}/assets/shapefile/shp1999_clipped.geojson`);
      
      if (response.ok) {
        const data = await response.json();
        this.polygonData = data;
        return this.ensureGeoJsonFormat(data);
      }
      
      // Fallback to shapefile if GeoJSON not available
      console.log('GeoJSON file not found, trying SHP file');
      const shpResponse = await fetch(`${process.env.PUBLIC_URL}/assets/shapefile/shp1999_clipped.shp`);
      if (shpResponse.ok) {
        const buffer = await shpResponse.arrayBuffer();
        this.polygonData = await shpjs.parseShp(buffer, {encoding: 'utf-8'});
        return this.ensureGeoJsonFormat(this.polygonData);
      }
      
      throw new Error('Failed to load polygon data from GeoJSON or SHP');
    } catch (error) {
      console.error('Error loading polygon data:', error);
      throw error;
    }
  }

  /**
   * Get marker data from GeoJSON or convert from shapefile if needed
   * @returns {Promise<Object>} - The marker data in GeoJSON format
   */
  async getMarkerData() {
    try {
      // Coba ambil dari cache atau GeoJSON file langsung
      let response = await fetch(`${process.env.PUBLIC_URL}/assets/shapefile/combined_markers_20250517_075624.geojson`);
      
      if (response.ok) {
        const data = await response.json();
        this.markerData = data;
        return this.ensureGeoJsonFormat(data);
      }
      
      // Fallback to shapefile if GeoJSON not available
      console.log('GeoJSON file not found, trying SHP file');
      const shpResponse = await fetch(`${process.env.PUBLIC_URL}/assets/shapefile/combined_markers_20250517_075624.shp`);
      if (shpResponse.ok) {
        const buffer = await shpResponse.arrayBuffer();
        this.markerData = await shpjs.parseShp(buffer, {encoding: 'utf-8'});
        return this.ensureGeoJsonFormat(this.markerData);
      }
      
      throw new Error('Failed to load marker data from GeoJSON or SHP');
    } catch (error) {
      console.error('Error loading marker data:', error);
      throw error;
    }
  }

  /**
   * Load shapefile data from the specified paths
   * @param {string} polygonPath - Path to the polygon shapefile
   * @param {string} markerPath - Path to the marker shapefile
   * @returns {Promise<{polygonData: any, markerData: any}>} - The loaded shapefile data
   */
  async loadShapefiles(polygonPath, markerPath) {
    this.isLoading = true;
    this.error = null;

    try {
      // Fetch the shapefiles as ArrayBuffer
      const [polygonResponse, markerResponse] = await Promise.all([
        fetch(polygonPath),
        fetch(markerPath)
      ]);
      
      if (!polygonResponse.ok) {
        throw new Error(`Failed to fetch polygon shapefile: ${polygonResponse.statusText}`);
      }
      
      if (!markerResponse.ok) {
        throw new Error(`Failed to fetch marker shapefile: ${markerResponse.statusText}`);
      }
      
      const [polygonBuffer, markerBuffer] = await Promise.all([
        polygonResponse.arrayBuffer(),
        markerResponse.arrayBuffer()
      ]);
      
      // Convert ArrayBuffer to GeoJSON using shpjs
      this.polygonData = await shpjs.parseShp(polygonBuffer, {encoding: 'utf-8'});
      const polygonDbf = await shpjs.parseDbf(polygonBuffer, {encoding: 'utf-8'});
      if (polygonDbf) {
        this.polygonData = shpjs.combine([this.polygonData, polygonDbf]);
      }
      
      this.markerData = await shpjs.parseShp(markerBuffer, {encoding: 'utf-8'});
      const markerDbf = await shpjs.parseDbf(markerBuffer, {encoding: 'utf-8'});
      if (markerDbf) {
        this.markerData = shpjs.combine([this.markerData, markerDbf]);
      }
      
      // Alternative approach if the above doesn't work
      if (!this.polygonData || !this.markerData) {
        console.log('Trying alternative approach to load shapefiles');
        this.polygonData = await shpjs(polygonPath);
        this.markerData = await shpjs(markerPath);
      }

      console.log('Polygon data:', this.polygonData);
      console.log('Marker data:', this.markerData);

      this.isLoading = false;
      return {
        polygonData: this.polygonData,
        markerData: this.markerData
      };
    } catch (error) {
      this.isLoading = false;
      this.error = error.message;
      console.error('Error loading shapefiles:', error);
      throw error;
    }
  }

  /**
   * Ensure the data is formatted as valid GeoJSON
   * @param {Object} data - The data to format
   * @returns {Object} - Properly formatted GeoJSON
   */
  ensureGeoJsonFormat(data) {
    if (!data) return null;
    
    // If it's already a GeoJSON object with features, return it
    if (data.type === 'FeatureCollection' && Array.isArray(data.features)) {
      return data;
    }
    
    // If it's a single feature
    if (data.type === 'Feature' && data.geometry) {
      return {
        type: 'FeatureCollection',
        features: [data]
      };
    }
    
    // If it's an array of features
    if (Array.isArray(data)) {
      return {
        type: 'FeatureCollection',
        features: data
      };
    }
    
    // If it's a raw geometry object
    if (data.type && ['Point', 'MultiPoint', 'LineString', 'MultiLineString', 'Polygon', 'MultiPolygon'].includes(data.type)) {
      return {
        type: 'FeatureCollection',
        features: [{
          type: 'Feature',
          geometry: data,
          properties: {}
        }]
      };
    }
    
    console.error('Could not convert data to GeoJSON format:', data);
    return null;
  }

  /**
   * Check if the data is currently loading
   * @returns {boolean} - True if the data is loading, false otherwise
   */
  isDataLoading() {
    return this.isLoading;
  }

  /**
   * Get the error message if there was an error loading the data
   * @returns {string|null} - The error message, or null if there was no error
   */
  getError() {
    return this.error;
  }

  /**
   * Create GeoJSON versions of shapefiles and download them
   * @param {string} polygonPath - Path to polygon shapefile 
   * @param {string} markerPath - Path to marker shapefile
   */
  async createGeoJsonVersions(polygonPath, markerPath) {
    try {
      // Load polygon data
      const polygonData = await shpjs(polygonPath);
      if (polygonData) {
        const formattedPolygon = this.ensureGeoJsonFormat(polygonData);
        this.downloadGeoJson(formattedPolygon, 'shp1999_clipped.geojson');
      }
      
      // Load marker data
      const markerData = await shpjs(markerPath);
      if (markerData) {
        const formattedMarker = this.ensureGeoJsonFormat(markerData);
        this.downloadGeoJson(formattedMarker, 'combined_markers_20250517_075624.geojson');
      }
      
      return {
        polygon: !!polygonData,
        marker: !!markerData
      };
    } catch (error) {
      console.error('Error creating GeoJSON versions:', error);
      return {
        polygon: false,
        marker: false,
        error: error.message
      };
    }
  }
  
  /**
   * Download GeoJSON data as a file
   * @param {Object} geoJsonData - The GeoJSON data to download
   * @param {string} filename - The filename to use
   */
  downloadGeoJson(geoJsonData, filename) {
    if (!geoJsonData) return;
    
    const blob = new Blob([JSON.stringify(geoJsonData)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * Convert shapefile to GeoJSON
   * @param {string} shpPath - Path to the .shp file
   * @param {string} dbfPath - Path to the .dbf file
   * @param {string} shxPath - Path to the .shx file
   * @returns {Promise<object>} - GeoJSON object
   */
  async convertShapefileToGeoJSON(shpPath, dbfPath, shxPath) {
    try {
      // Read files as base64 strings
      const shpData = await RNFS.readFile(shpPath, 'base64');
      const dbfData = await RNFS.readFile(dbfPath, 'base64');
      const shxData = await RNFS.readFile(shxPath, 'base64');

      // Convert base64 to Buffer
      const shpBuffer = Buffer.from(shpData, 'base64');
      const dbfBuffer = Buffer.from(dbfData, 'base64');
      const shxBuffer = Buffer.from(shxData, 'base64');

      // Parse shapefile using shpjs
      const parsedShp = await shpjs.parseShp(shpBuffer, shxBuffer);
      const parsedDbf = await shpjs.parseDbf(dbfBuffer);

      // Combine the parsed data into a GeoJSON object
      const geojson = await shpjs.combine([parsedShp, parsedDbf]);

      return geojson;
    } catch (error) {
      console.error('Error converting shapefile to GeoJSON:', error);
      throw error;
    }
  }

  /**
   * Process the GeoJSON polygon data to be used with react-native-maps
   * @param {object} geojson - GeoJSON object containing polygon features
   * @returns {array} - Array of polygon coordinates formatted for react-native-maps
   */
  processPolygonData(geojson) {
    if (!geojson || !geojson.features) {
      return [];
    }

    return geojson.features
      .map(feature => {
        if (feature.geometry && feature.geometry.type === 'Polygon') {
          return feature.geometry.coordinates[0].map(coord => ({
            latitude: coord[1],
            longitude: coord[0]
          }));
        }
        return [];
      })
      .filter(polygon => polygon.length > 0);
  }

  /**
   * Process the GeoJSON marker data to be used with react-native-maps
   * @param {object} geojson - GeoJSON object containing point features
   * @returns {array} - Array of marker data formatted for react-native-maps
   */
  processMarkerData(geojson) {
    if (!geojson || !geojson.features) {
      return [];
    }

    return geojson.features
      .map(feature => {
        if (feature.geometry && feature.geometry.type === 'Point') {
          return {
            coordinate: {
              latitude: feature.geometry.coordinates[1],
              longitude: feature.geometry.coordinates[0]
            },
            title: feature.properties?.name || 'Marker',
            description: feature.properties?.description || 'Boundary Marker',
            properties: feature.properties
          };
        }
        return null;
      })
      .filter(marker => marker !== null);
  }

  /**
   * Copy a shapefile from assets to the document directory if it doesn't exist
   * @param {string} basename - Base name of the shapefile (without extension)
   * @returns {Promise<object>} - Paths to the copied files
   */
  async copyShapefileIfNeeded(basename) {
    const destShp = RNFS.DocumentDirectoryPath + `/${basename}.shp`;
    const destDbf = RNFS.DocumentDirectoryPath + `/${basename}.dbf`;
    const destShx = RNFS.DocumentDirectoryPath + `/${basename}.shx`;
    
    // Check if files already exist
    const shpExists = await RNFS.exists(destShp);
    const dbfExists = await RNFS.exists(destDbf);
    const shxExists = await RNFS.exists(destShx);
    
    if (!shpExists || !dbfExists || !shxExists) {
      // Copy files from assets
      try {
        await RNFS.copyFileAssets(`shapefile/${basename}.shp`, destShp);
        await RNFS.copyFileAssets(`shapefile/${basename}.dbf`, destDbf);
        await RNFS.copyFileAssets(`shapefile/${basename}.shx`, destShx);
        console.log(`Copied ${basename} shapefile files to document directory`);
      } catch (error) {
        console.error(`Error copying ${basename} shapefile files:`, error);
        throw error;
      }
    }
    
    return {
      shpPath: destShp,
      dbfPath: destDbf,
      shxPath: destShx
    };
  }
}

// Create a singleton instance
const shapefileService = new ShapefileService();
export default shapefileService;
