/**
 * <PERSON>ok Rebinmas Mobile App
 * Offline boundary marker navigation application
 *
 * @format
 */

import React, { useEffect } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  PermissionsAndroid,
  Platform,
  Alert,
  useColorScheme,
} from 'react-native';

// Import our map component
import MapComponent from './src/components/MapComponent';

function App(): React.JSX.Element {
  // Request location permissions when the app starts
  useEffect(() => {
    requestLocationPermission();
  }, []);

  // Function to request location permission
  const requestLocationPermission = async () => {
    if (Platform.OS === 'ios') {
      // iOS permission is requested through info.plist
      return;
    }

    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: 'Patok Rebinmas needs access to your location to show your position on the map.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );

      if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
        Alert.alert(
          'Permission Denied',
          'You need to grant location permission to use the navigation features of this app.',
          [{ text: 'OK' }]
        );
      }
    } catch (err) {
      console.warn('Error requesting location permission:', err);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      <MapComponent />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
