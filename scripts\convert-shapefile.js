const fs = require('fs');
const path = require('path');
const shp = require('shpjs');

// Paths to the shapefile and output GeoJSON
const POLYGON_SHP_PATH = path.resolve(__dirname, '../android/app/src/main/assets/shapefile/shp1999_clipped.shp');
const MARKER_SHP_PATH = path.resolve(__dirname, '../android/app/src/main/assets/shapefile/combined_markers_20250517_075624.shp');
const POLYGON_GEOJSON_PATH = path.resolve(__dirname, '../android/app/src/main/assets/shapefile/polygon_data.json');
const MARKER_GEOJSON_PATH = path.resolve(__dirname, '../android/app/src/main/assets/shapefile/marker_data.json');

// Function to read a file as a buffer
function readFileAsBuffer(filePath) {
  return new Promise((resolve, reject) => {
    fs.readFile(filePath, (err, data) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(data.buffer);
    });
  });
}

// Function to write GeoJSON to a file
function writeGeoJson(filePath, data) {
  return new Promise((resolve, reject) => {
    fs.writeFile(filePath, JSON.stringify(data), 'utf8', (err) => {
      if (err) {
        reject(err);
        return;
      }
      resolve();
    });
  });
}

// Function to convert a shapefile to GeoJSON
async function convertShapefileToGeoJson(shpPath, outputPath) {
  try {
    console.log(`Converting ${shpPath} to GeoJSON...`);
    
    // Read the shapefile
    const buffer = await readFileAsBuffer(shpPath);
    
    // Convert to GeoJSON
    const geojson = await shp(buffer);
    
    // Write the GeoJSON to a file
    await writeGeoJson(outputPath, geojson);
    
    console.log(`GeoJSON saved to ${outputPath}`);
  } catch (error) {
    console.error(`Error converting shapefile to GeoJSON:`, error);
  }
}

// Convert both shapefiles
async function convertShapefiles() {
  try {
    // Create the output directory if it doesn't exist
    const outputDir = path.dirname(POLYGON_GEOJSON_PATH);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Convert the polygon shapefile
    await convertShapefileToGeoJson(POLYGON_SHP_PATH, POLYGON_GEOJSON_PATH);
    
    // Convert the marker shapefile
    await convertShapefileToGeoJson(MARKER_SHP_PATH, MARKER_GEOJSON_PATH);
    
    console.log('Conversion complete!');
  } catch (error) {
    console.error('Error converting shapefiles:', error);
  }
}

// Run the conversion
convertShapefiles();
